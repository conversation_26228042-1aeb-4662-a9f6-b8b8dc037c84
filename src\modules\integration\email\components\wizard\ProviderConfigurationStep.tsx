import React, { useState, useRef, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Typography,
  Button,
  Input,
  FormItem,
  Form,
  Card,
  Icon,
  Alert,
  Modal,
  Select,
  Textarea,
} from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import { EmailProvider, ProviderConfigurationData } from '../../types/providers';
import { useFormErrors } from '@/shared/hooks/form';
import {
  useTestProviderConnection,
  useValidateProviderConfiguration,
  useOAuthFlow
} from '../../hooks/useProviders';
import { getProviderSchema } from '../../schemas/providers';
import { SetupGuideModal } from '../provider';

interface ProviderConfigurationStepProps {
  provider: EmailProvider;
  initialData?: ProviderConfigurationData;
  onConfigurationChange: (data: ProviderConfigurationData) => void;
  onNext: () => void;
  onBack: () => void;
  onTestConnection?: (data: ProviderConfigurationData) => Promise<boolean>;
  isSubmitting?: boolean;
}

/**
 * ProviderConfigurationStep Component
 * Bước cấu hình chi tiết cho provider được chọn
 */
const ProviderConfigurationStep: React.FC<ProviderConfigurationStepProps> = ({
  provider,
  initialData,
  onConfigurationChange,
  onNext,
  onBack,
  onTestConnection,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const { formRef, setFormErrors } = useFormErrors<Record<string, unknown>>();

  const [showSetupGuide, setShowSetupGuide] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // Provider hooks
  const testConnectionMutation = useTestProviderConnection();
  const validateConfigMutation = useValidateProviderConfiguration();
  const oauthFlow = useOAuthFlow(provider.id);

  // Form default values based on provider
  const defaultValues = useMemo(() => {
    const values: Record<string, unknown> = {
      serverName: `${provider.displayName} Server`,
      host: provider.defaultConfig.host,
      port: provider.defaultConfig.port,
      useSsl: provider.defaultConfig.useSsl,
      useStartTls: provider.defaultConfig.useStartTls,
      isActive: true,
      authMethod: provider.authMethods[0], // Default to first auth method
    };

    // Add initial data if provided
    if (initialData) {
      Object.assign(values, {
        serverName: initialData.providerName || values.serverName,
        ...initialData.credentials,
        authMethod: initialData.authMethod,
        ...initialData.customSettings,
      });
    }

    return values;
  }, [provider, initialData]);

  // Dynamic validation schema based on provider
  const validationSchema = useMemo(() => {
    // Use provider-specific schema if available
    return getProviderSchema(provider.id);
  }, [provider.id]);

  const handleFormSubmit = useCallback(async (values: Record<string, unknown>) => {
    try {
      // Validate form data
      const validatedData = validationSchema.parse(values);
      
      // Create configuration data
      const configData: ProviderConfigurationData = {
        providerId: provider.id,
        providerName: provider.displayName,
        authMethod: validatedData.authMethod as any,
        credentials: {
          username: validatedData.username as string,
          password: validatedData.password as string,
          apiKey: validatedData.apiKey as string,
          clientId: validatedData.clientId as string,
          clientSecret: validatedData.clientSecret as string,
        },
        customSettings: {},
      };

      // Add custom fields to settings
      provider.customFields?.forEach(field => {
        if (validatedData[field.name] !== undefined) {
          configData.customSettings![field.name] = validatedData[field.name] as string | number | boolean;
        }
      });

      onConfigurationChange(configData);
      onNext();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formErrors: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path.length > 0) {
            formErrors[err.path[0] as string] = err.message;
          }
        });
        setFormErrors(formErrors);
      }
    }
  }, [provider, validationSchema, onConfigurationChange, onNext, setFormErrors]);

  const handleTestConnection = useCallback(async () => {
    setTestResult(null);

    try {
      // Get current form data
      const formData = formRef.current?.getValues() as Record<string, unknown>;

      const configData: ProviderConfigurationData = {
        providerId: provider.id,
        providerName: provider.displayName,
        authMethod: formData.authMethod as any,
        credentials: {
          username: formData.username as string,
          password: formData.password as string,
          apiKey: formData.apiKey as string,
          clientId: formData.clientId as string,
          clientSecret: formData.clientSecret as string,
        },
        customSettings: {},
      };

      // Add custom fields to settings
      provider.customFields?.forEach(field => {
        if (formData[field.name] !== undefined) {
          configData.customSettings![field.name] = formData[field.name] as string | number | boolean;
        }
      });

      const testData = {
        providerId: provider.id,
        configuration: configData,
        testEmail: {
          recipientEmail: '<EMAIL>', // This should come from a form field
          subject: t('integration:wizard.configuration.testSubject'),
          body: t('integration:wizard.configuration.testBody'),
        },
      };

      const result = await testConnectionMutation.mutateAsync(testData);
      setTestResult({
        success: result.success,
        message: result.message
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: t('integration:wizard.configuration.testError')
      });
    }
  }, [provider, formRef, t, testConnectionMutation]);

  const renderCustomField = (field: any) => {
    switch (field.type) {
      case 'select':
        return (
          <Select
            options={field.options || []}
            placeholder={field.placeholder}
            fullWidth
          />
        );
      case 'textarea':
        return (
          <Textarea
            placeholder={field.placeholder}
            rows={3}
            fullWidth
          />
        );
      case 'number':
        return (
          <Input
            type="number"
            placeholder={field.placeholder}
            fullWidth
            min={field.validation?.min}
            max={field.validation?.max}
          />
        );
      default:
        return (
          <Input
            type={field.type}
            placeholder={field.placeholder}
            fullWidth
          />
        );
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-8 h-8 flex items-center justify-center bg-gray-100 rounded">
            {provider.logoUrl ? (
              <img
                src={provider.logoUrl}
                alt={provider.displayName}
                className="w-6 h-6 object-contain"
              />
            ) : (
              <Icon name="mail" className="w-4 h-4 text-gray-500" />
            )}
          </div>
          <Typography variant="h4">
            {t('integration:wizard.configuration.title', { provider: provider.displayName })}
          </Typography>
        </div>
        <Typography variant="body1" className="text-muted-foreground">
          {t('integration:wizard.configuration.description')}
        </Typography>
      </div>

      {/* Setup Guide Alert */}
      <Alert variant="info" className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <Typography variant="body2">
              {t('integration:wizard.configuration.needHelp')}
            </Typography>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSetupGuide(true)}
          >
            <Icon name="help-circle" className="w-4 h-4 mr-2" />
            {t('integration:wizard.configuration.viewGuide')}
          </Button>
        </div>
      </Alert>

      {/* Configuration Form */}
      <Card>
        <div className="p-6">
          <Form
            schema={validationSchema}
            onSubmit={handleFormSubmit}
            defaultValues={defaultValues}
            ref={formRef}
            submitOnEnter={false}
          >
            <div className="space-y-6">
              {/* Basic Configuration */}
              <div>
                <Typography variant="h6" className="mb-4">
                  {t('integration:wizard.configuration.basicSettings')}
                </Typography>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem
                    name="serverName"
                    label={t('integration:wizard.configuration.serverName')}
                    required
                  >
                    <Input
                      placeholder={t('integration:wizard.configuration.serverNamePlaceholder')}
                      fullWidth
                    />
                  </FormItem>

                  <FormItem
                    name="host"
                    label={t('integration:wizard.configuration.host')}
                    required
                  >
                    <Input
                      placeholder={provider.defaultConfig.host}
                      fullWidth
                      disabled
                    />
                  </FormItem>

                  <FormItem
                    name="port"
                    label={t('integration:wizard.configuration.port')}
                    required
                  >
                    <Input
                      type="number"
                      placeholder={provider.defaultConfig.port.toString()}
                      fullWidth
                      disabled
                    />
                  </FormItem>

                  <FormItem
                    name="username"
                    label={t('integration:wizard.configuration.username')}
                    required
                  >
                    <Input
                      type="email"
                      placeholder={t('integration:wizard.configuration.usernamePlaceholder')}
                      fullWidth
                    />
                  </FormItem>
                </div>
              </div>

              {/* Authentication */}
              <div>
                <Typography variant="h6" className="mb-4">
                  {t('integration:wizard.configuration.authentication')}
                </Typography>

                {provider.authMethods.includes('password') && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem
                      name="password"
                      label={t('integration:wizard.configuration.password')}
                      required
                    >
                      <Input
                        type="password"
                        placeholder={t('integration:wizard.configuration.passwordPlaceholder')}
                        fullWidth
                      />
                    </FormItem>
                  </div>
                )}
              </div>

              {/* Provider-specific fields */}
              {provider.customFields && provider.customFields.length > 0 && (
                <div>
                  <Typography variant="h6" className="mb-4">
                    {t('integration:wizard.configuration.providerSettings')}
                  </Typography>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {provider.customFields.map(field => (
                      <FormItem
                        key={field.name}
                        name={field.name}
                        label={field.label}
                        required={field.required}
                        description={field.description}
                      >
                        {renderCustomField(field)}
                      </FormItem>
                    ))}
                  </div>
                </div>
              )}

              {/* SSL/TLS Settings */}
              <div>
                <Typography variant="h6" className="mb-4">
                  {t('integration:wizard.configuration.securitySettings')}
                </Typography>
                
                <div className="space-y-4">
                  <FormItem name="useSsl" label={t('integration:wizard.configuration.useSsl')}>
                    <Toggle />
                  </FormItem>
                  
                  <FormItem name="useStartTls" label={t('integration:wizard.configuration.useStartTls')}>
                    <Toggle />
                  </FormItem>
                  
                  <FormItem name="isActive" label={t('integration:wizard.configuration.isActive')}>
                    <Toggle />
                  </FormItem>
                </div>
              </div>
            </div>

            {/* Test Connection */}
            <div className="mt-6 pt-6 border-t">
              <div className="flex items-center justify-between mb-4">
                <Typography variant="h6">
                  {t('integration:wizard.configuration.testConnection')}
                </Typography>
                <Button
                  variant="outline"
                  onClick={handleTestConnection}
                  disabled={testConnectionMutation.isLoading}
                >
                  {testConnectionMutation.isLoading ? (
                    <Icon name="loader" className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Icon name="zap" className="w-4 h-4 mr-2" />
                  )}
                  {t('integration:wizard.configuration.testNow')}
                </Button>
              </div>

              {testResult && (
                <Alert variant={testResult.success ? 'success' : 'error'}>
                  <Typography variant="body2">
                    {testResult.message}
                  </Typography>
                </Alert>
              )}
            </div>

            {/* Navigation */}
            <div className="flex justify-between mt-6 pt-6 border-t">
              <Button variant="outline" onClick={onBack}>
                <Icon name="arrow-left" className="w-4 h-4 mr-2" />
                {t('common:back')}
              </Button>
              
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <Icon name="loader" className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Icon name="check" className="w-4 h-4 mr-2" />
                )}
                {t('integration:wizard.configuration.save')}
              </Button>
            </div>
          </Form>
        </div>
      </Card>

      {/* Setup Guide Modal */}
      <SetupGuideModal
        isOpen={showSetupGuide}
        onClose={() => setShowSetupGuide(false)}
        provider={provider}
      />
    </div>
  );
};

export default ProviderConfigurationStep;
