import React, { useState } from 'react';
import { Icon, Card, Input, ResponsiveGrid } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';

// <PERSON>h sách tất cả các icon có sẵn
const allIcons: IconName[] = [
  'user',
  'language',
  'sun',
  'moon',
  'settings',
  'logout',
  'check',
  'star',
  'plus',
  'minus',
  'chevron-left',
  'chevron-right',
  'chevron-up',
  'chevron-down',
  'search',
  'paperclip',
  'send',
  'menu',
  'home',
  'animation',
  'components',
  'upload',
  'microphone',
  'close',
  'image',
  'pdf',
  'doc',
  'sheet',
  'presentation',
  'file',
  'warning',
  'info',
  'chat',
  'save',
  'link',
  'mail',
  'building',
  'document',
  'map-pin',
  'phone',
  'layout',
  'filter',
  'layers',
  'grid',
  'server',
  'list',
  'code',
  'loading',
  'calendar',
  'power',
  'edit',
  'eye',
  'eye-off',
  'lock',
  'help-circle',
  'alert-circle',
  'globe',
  'trash',
  'arrow-left',
  'rpoint',
  'shopping-cart',
  'copy',
  'x',
  'users',
  'campaign',
  'chart',
  'trending-up',
  'trending-down',
  'payment',
  'credit-card',
  'award',
  'workflow',
  'integration',
  'subscription',
  'toggle-on',
  'toggle-off',
  'filter-v2',
  'more-horizontal',
  'more-vertical',
  'business',
  'crawl',
  'file-media',
  'file-text',
  'box',
  'refresh-cw',
  'bar-chart',
  'package',
  'cloud',
  'tag',
  'facebook',
  'zap',
  'database',
  'website',
  'google',
  'sms',
  'message-square',
  'credit-card',
  'text',
  'hash',
  'toggle-left',
];

const IconsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [copiedIcon, setCopiedIcon] = useState<string | null>(null);

  // Lọc icon theo từ khóa tìm kiếm
  const filteredIcons = allIcons.filter(icon =>
    icon.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Hàm copy tên icon vào clipboard
  const copyIconName = async (iconName: string) => {
    try {
      await navigator.clipboard.writeText(iconName);
      setCopiedIcon(iconName);
      setTimeout(() => setCopiedIcon(null), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          Icon Library
        </h1>
        <p className="text-muted mb-6">
          Tất cả các icon có sẵn trong hệ thống. Click vào icon để copy tên.
        </p>

        {/* Search input */}
        <div className="max-w-md">
          <Input
            type="text"
            placeholder="Tìm kiếm icon..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
        </div>
      </div>

      {/* Icon grid */}
      <ResponsiveGrid
        maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6, xl: 8 }}
        maxColumnsWithChatPanel={{ xs: 2, sm: 2, md: 3, lg: 4, xl: 6 }}
        gap={{ xs: 3, md: 4 }}
      >
        {filteredIcons.map(iconName => (
          <Card
            key={iconName}
            variant="bordered"
            hoverable
            className="p-4 cursor-pointer transition-all duration-200 hover:shadow-md"
            onClick={() => copyIconName(iconName)}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className="text-primary">
                <Icon name={iconName} size="xl" />
              </div>
              <div className="w-full">
                <p className="text-xs font-medium text-foreground truncate">
                  {iconName}
                </p>
                {copiedIcon === iconName && (
                  <p className="text-xs text-green-600 mt-1">Đã copy!</p>
                )}
              </div>
            </div>
          </Card>
        ))}
      </ResponsiveGrid>

      {filteredIcons.length === 0 && (
        <div className="text-center py-12">
          <Icon name="search" size="xl" className="text-muted mx-auto mb-4" />
          <p className="text-muted">Không tìm thấy icon nào với từ khóa "{searchTerm}"</p>
        </div>
      )}

      {/* Usage example */}
      <div className="mt-12 p-6 bg-muted/30 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">Cách sử dụng:</h3>
        <div className="space-y-2 text-sm">
          <p>
            <code className="bg-background px-2 py-1 rounded text-primary">
              {'<Icon name="user" size="md" />'}
            </code>
          </p>
          <p>
            <code className="bg-background px-2 py-1 rounded text-primary">
              {'<Icon name="settings" size="lg" color="#3B82F6" />'}
            </code>
          </p>
        </div>
      </div>
    </div>
  );
};

export default IconsPage;
