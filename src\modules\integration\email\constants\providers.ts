/**
 * Email Provider Constants and Definitions
 */

import { EmailProvider } from '../types/providers';

/**
 * Tier 1 Providers - Personal Email Services
 */
export const PERSONAL_EMAIL_PROVIDERS: EmailProvider[] = [
  {
    id: 'gmail',
    name: 'gmail',
    displayName: 'Gmail',
    description: 'Google Gmail - Dịch vụ email cá nhân phổ biến nhất',
    logo: '/assets/providers/gmail.svg',
    category: 'personal',
    tier: 1,
    isPopular: true,
    defaultConfig: {
      host: 'smtp.gmail.com',
      port: 587,
      useSsl: false,
      useStartTls: true,
      requiresAuth: true,
    },
    authMethods: ['password', 'oauth'],
    customFields: [
      {
        name: 'appPassword',
        label: 'App Password',
        type: 'password',
        required: true,
        placeholder: 'xxxx xxxx xxxx xxxx',
        description: '<PERSON><PERSON> dụng App Password thay vì mật khẩu thường',
      },
    ],
    setupGuide: {
      title: 'Cấu hình Gmail SMTP',
      description: 'Hướng dẫn thiết lập Gmail để gửi email qua SMTP',
      steps: [
        {
          title: 'Bật 2-Factor Authentication',
          description: 'Truy cập Google Account Settings và bật 2FA',
        },
        {
          title: 'Tạo App Password',
          description: 'Tạo App Password cho ứng dụng email',
          code: 'Google Account > Security > App passwords',
        },
        {
          title: 'Sử dụng App Password',
          description: 'Dùng App Password thay vì mật khẩu thường',
        },
      ],
      documentationUrl: 'https://support.google.com/accounts/answer/185833',
    },
    features: ['Free 15GB storage', 'Reliable delivery', 'OAuth support'],
    limitations: ['Requires App Password', 'Daily sending limits'],
    pricing: {
      free: '15GB miễn phí',
      paid: 'Google Workspace từ $6/tháng',
      url: 'https://workspace.google.com/pricing/',
    },
    isActive: true,
  },
  {
    id: 'outlook',
    name: 'outlook',
    displayName: 'Outlook',
    description: 'Microsoft Outlook - Dịch vụ email doanh nghiệp',
    logo: '/assets/providers/outlook.svg',
    category: 'personal',
    tier: 1,
    isPopular: true,
    defaultConfig: {
      host: 'smtp-mail.outlook.com',
      port: 587,
      useSsl: false,
      useStartTls: true,
      requiresAuth: true,
    },
    authMethods: ['password', 'oauth'],
    setupGuide: {
      title: 'Cấu hình Outlook SMTP',
      description: 'Hướng dẫn thiết lập Outlook để gửi email qua SMTP',
      steps: [
        {
          title: 'Bật SMTP Authentication',
          description: 'Truy cập Outlook settings và bật SMTP auth',
        },
        {
          title: 'Cấu hình App Password',
          description: 'Tạo App Password nếu bật 2FA',
        },
      ],
      documentationUrl: 'https://support.microsoft.com/en-us/office/pop-imap-and-smtp-settings-for-outlook-com-d088b986-291d-42b8-9564-9c414e2aa040',
    },
    features: ['Free 15GB storage', 'Office 365 integration', 'OAuth support'],
    isActive: true,
  },
  {
    id: 'yahoo',
    name: 'yahoo',
    displayName: 'Yahoo Mail',
    description: 'Yahoo Mail - Dịch vụ email miễn phí',
    logo: '/assets/providers/yahoo.svg',
    category: 'personal',
    tier: 1,
    isPopular: false,
    defaultConfig: {
      host: 'smtp.mail.yahoo.com',
      port: 587,
      useSsl: false,
      useStartTls: true,
      requiresAuth: true,
    },
    authMethods: ['password'],
    customFields: [
      {
        name: 'appPassword',
        label: 'App Password',
        type: 'password',
        required: true,
        placeholder: 'App Password từ Yahoo',
        description: 'Tạo App Password trong Yahoo Account Security',
      },
    ],
    setupGuide: {
      title: 'Cấu hình Yahoo Mail SMTP',
      description: 'Hướng dẫn thiết lập Yahoo Mail để gửi email qua SMTP',
      steps: [
        {
          title: 'Bật 2-Step Verification',
          description: 'Truy cập Yahoo Account Security và bật 2-step verification',
        },
        {
          title: 'Tạo App Password',
          description: 'Tạo App Password cho ứng dụng email',
        },
      ],
      documentationUrl: 'https://help.yahoo.com/kb/SLN4724.html',
    },
    features: ['Free 1TB storage', 'Simple setup'],
    limitations: ['Requires App Password', 'Limited API features'],
    isActive: true,
  },
];

/**
 * Tier 1 Business Providers - Transactional Email Services
 */
export const BUSINESS_EMAIL_PROVIDERS: EmailProvider[] = [
  {
    id: 'sendgrid',
    name: 'sendgrid',
    displayName: 'SendGrid',
    description: 'SendGrid - Dịch vụ email transactional hàng đầu',
    logo: '/assets/providers/sendgrid.svg',
    category: 'transactional',
    tier: 1,
    isPopular: true,
    defaultConfig: {
      host: 'smtp.sendgrid.net',
      port: 587,
      useSsl: false,
      useStartTls: true,
      requiresAuth: true,
    },
    authMethods: ['apikey'],
    customFields: [
      {
        name: 'apiKey',
        label: 'API Key',
        type: 'password',
        required: true,
        placeholder: 'SG.xxxxxxxxxx',
        description: 'API Key từ SendGrid Dashboard',
      },
    ],
    setupGuide: {
      title: 'Cấu hình SendGrid SMTP',
      description: 'Hướng dẫn thiết lập SendGrid để gửi email transactional',
      steps: [
        {
          title: 'Tạo API Key',
          description: 'Truy cập SendGrid Dashboard > Settings > API Keys',
        },
        {
          title: 'Cấu hình Domain Authentication',
          description: 'Xác thực domain để tăng deliverability',
        },
      ],
      documentationUrl: 'https://docs.sendgrid.com/for-developers/sending-email/getting-started-smtp',
    },
    features: ['99.9% uptime', 'Advanced analytics', 'Template engine'],
    pricing: {
      free: '100 emails/ngày miễn phí',
      paid: 'Từ $14.95/tháng',
      url: 'https://sendgrid.com/pricing/',
    },
    isActive: true,
  },
  {
    id: 'mailgun',
    name: 'mailgun',
    displayName: 'Mailgun',
    description: 'Mailgun - Email API cho developers',
    logo: '/assets/providers/mailgun.svg',
    category: 'transactional',
    tier: 1,
    isPopular: true,
    defaultConfig: {
      host: 'smtp.mailgun.org',
      port: 587,
      useSsl: false,
      useStartTls: true,
      requiresAuth: true,
    },
    authMethods: ['apikey', 'password'],
    customFields: [
      {
        name: 'domain',
        label: 'Domain',
        type: 'text',
        required: true,
        placeholder: 'mg.yourdomain.com',
        description: 'Domain đã verify trong Mailgun',
      },
      {
        name: 'apiKey',
        label: 'API Key',
        type: 'password',
        required: true,
        placeholder: 'key-xxxxxxxxxx',
        description: 'Private API Key từ Mailgun Dashboard',
      },
    ],
    setupGuide: {
      title: 'Cấu hình Mailgun SMTP',
      description: 'Hướng dẫn thiết lập Mailgun để gửi email',
      steps: [
        {
          title: 'Verify Domain',
          description: 'Thêm và verify domain trong Mailgun Dashboard',
        },
        {
          title: 'Lấy API Key',
          description: 'Copy Private API Key từ Settings > API Keys',
        },
      ],
      documentationUrl: 'https://documentation.mailgun.com/en/latest/quickstart-sending.html',
    },
    features: ['Powerful API', 'Email validation', 'Detailed logs'],
    pricing: {
      free: '5,000 emails/tháng miễn phí',
      paid: 'Từ $35/tháng',
      url: 'https://www.mailgun.com/pricing/',
    },
    isActive: true,
  },
];

/**
 * Tier 2 Providers - Additional Business Services
 */
export const TIER2_EMAIL_PROVIDERS: EmailProvider[] = [
  {
    id: 'amazon-ses',
    name: 'amazon-ses',
    displayName: 'Amazon SES',
    description: 'Amazon Simple Email Service - Dịch vụ email AWS',
    logo: '/assets/providers/amazon-ses.svg',
    category: 'transactional',
    tier: 1,
    isPopular: true,
    defaultConfig: {
      host: 'email-smtp.us-east-1.amazonaws.com',
      port: 587,
      useSsl: false,
      useStartTls: true,
      requiresAuth: true,
    },
    authMethods: ['password'],
    customFields: [
      {
        name: 'region',
        label: 'AWS Region',
        type: 'select',
        required: true,
        options: [
          { label: 'US East (N. Virginia)', value: 'us-east-1' },
          { label: 'US West (Oregon)', value: 'us-west-2' },
          { label: 'Europe (Ireland)', value: 'eu-west-1' },
          { label: 'Asia Pacific (Singapore)', value: 'ap-southeast-1' },
        ],
        description: 'Chọn AWS region gần nhất',
      },
      {
        name: 'accessKeyId',
        label: 'Access Key ID',
        type: 'text',
        required: true,
        placeholder: 'AKIAIOSFODNN7EXAMPLE',
        description: 'AWS Access Key ID có quyền SES',
      },
      {
        name: 'secretAccessKey',
        label: 'Secret Access Key',
        type: 'password',
        required: true,
        placeholder: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
        description: 'AWS Secret Access Key',
      },
    ],
    setupGuide: {
      title: 'Cấu hình Amazon SES',
      description: 'Hướng dẫn thiết lập Amazon SES để gửi email',
      steps: [
        {
          title: 'Tạo IAM User',
          description: 'Tạo IAM user với quyền AmazonSESFullAccess',
        },
        {
          title: 'Verify Email/Domain',
          description: 'Verify email hoặc domain trong SES Console',
        },
        {
          title: 'Request Production Access',
          description: 'Request để thoát khỏi sandbox mode',
        },
      ],
      documentationUrl: 'https://docs.aws.amazon.com/ses/latest/dg/send-email-smtp.html',
    },
    features: ['Pay-as-you-go', 'High deliverability', 'AWS integration'],
    pricing: {
      free: '62,000 emails/tháng miễn phí (từ EC2)',
      paid: '$0.10 per 1,000 emails',
      url: 'https://aws.amazon.com/ses/pricing/',
    },
    regions: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1'],
    isActive: true,
  },
  {
    id: 'mailchimp-transactional',
    name: 'mailchimp-transactional',
    displayName: 'Mailchimp Transactional',
    description: 'Mailchimp Transactional (Mandrill) - Email transactional',
    logo: '/assets/providers/mailchimp.svg',
    category: 'transactional',
    tier: 2,
    isPopular: false,
    defaultConfig: {
      host: 'smtp.mandrillapp.com',
      port: 587,
      useSsl: false,
      useStartTls: true,
      requiresAuth: true,
    },
    authMethods: ['apikey'],
    customFields: [
      {
        name: 'apiKey',
        label: 'API Key',
        type: 'password',
        required: true,
        placeholder: 'md-xxxxxxxxxx',
        description: 'API Key từ Mailchimp Transactional',
      },
    ],
    setupGuide: {
      title: 'Cấu hình Mailchimp Transactional',
      description: 'Hướng dẫn thiết lập Mailchimp Transactional',
      steps: [
        {
          title: 'Tạo API Key',
          description: 'Truy cập Mailchimp > Transactional > Settings > SMTP & API Info',
        },
        {
          title: 'Verify Domain',
          description: 'Thêm và verify sending domain',
        },
      ],
      documentationUrl: 'https://mailchimp.com/developer/transactional/guides/send-first-email/',
    },
    features: ['Mailchimp integration', 'Template support', 'Analytics'],
    pricing: {
      paid: 'Từ $20/tháng',
      url: 'https://mailchimp.com/pricing/transactional-email/',
    },
    isActive: true,
  },
];

/**
 * All Email Providers Combined
 */
export const ALL_EMAIL_PROVIDERS: EmailProvider[] = [
  ...PERSONAL_EMAIL_PROVIDERS,
  ...BUSINESS_EMAIL_PROVIDERS,
  ...TIER2_EMAIL_PROVIDERS,
];

/**
 * Provider Categories for Filtering
 */
export const PROVIDER_CATEGORIES = [
  { value: 'personal', label: 'Email cá nhân', icon: 'user' },
  { value: 'business', label: 'Email doanh nghiệp', icon: 'building' },
  { value: 'transactional', label: 'Email giao dịch', icon: 'send' },
] as const;

/**
 * Provider Tiers for Filtering
 */
export const PROVIDER_TIERS = [
  { value: 1, label: 'Ưu tiên cao', description: 'Providers phổ biến nhất' },
  { value: 2, label: 'Ưu tiên trung bình', description: 'Providers chuyên nghiệp' },
  { value: 3, label: 'Ưu tiên thấp', description: 'Providers đặc biệt' },
] as const;

/**
 * Authentication Methods
 */
export const AUTH_METHODS = [
  { value: 'password', label: 'Username/Password', icon: 'key' },
  { value: 'oauth', label: 'OAuth 2.0', icon: 'shield' },
  { value: 'apikey', label: 'API Key', icon: 'code' },
] as const;

/**
 * Get provider by ID
 */
export const getProviderById = (id: string): EmailProvider | undefined => {
  return ALL_EMAIL_PROVIDERS.find(provider => provider.id === id);
};

/**
 * Get providers by category
 */
export const getProvidersByCategory = (category: string): EmailProvider[] => {
  return ALL_EMAIL_PROVIDERS.filter(provider => provider.category === category);
};

/**
 * Get popular providers
 */
export const getPopularProviders = (): EmailProvider[] => {
  return ALL_EMAIL_PROVIDERS.filter(provider => provider.isPopular);
};
