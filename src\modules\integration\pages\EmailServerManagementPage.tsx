import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ConfirmDeleteModal, Button, Icon, Typography } from '@/shared/components/common';
import { SortOrder, TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';

import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

import {
  useEmailServers,
  useCreateEmailServer,
  useUpdateEmailServer,
  useDeleteEmailServer,
} from '../email/hooks';
import {
  EmailServerConfiguration,
  EmailServerQueryParams,
  CreateEmailServerDto,
  UpdateEmailServerDto,
  CreateEmailServerV2Dto,
} from '../email/types';

import EmailServerForm from '../components/EmailServerForm';
import { EmailServerWizard } from '../email/components/wizard';

/**
 * Trang quản lý Email Server
 */
const EmailServerManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [emailServers, setEmailServers] = useState<EmailServerConfiguration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const [serverToDelete, setServerToDelete] = useState<EmailServerConfiguration | null>(null);
  const [serverToEdit, setServerToEdit] = useState<EmailServerConfiguration | null>(null);
  const [serverToView, setServerToView] = useState<EmailServerConfiguration | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showWizard, setShowWizard] = useState(false);
  const [useWizardMode, setUseWizardMode] = useState(true); // Toggle between wizard and legacy form

  // Sử dụng hook animation cho form tạo mới
  const { isVisible: isCreateFormVisible, showForm: showCreateForm, hideForm: hideCreateForm } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const { isVisible: isEditFormVisible, showForm: showEditForm, hideForm: hideEditForm } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const { isVisible: isViewFormVisible, showForm: showViewForm, hideForm: hideViewForm } = useSlideForm();

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((server: EmailServerConfiguration) => {
    setServerToDelete(server);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hiển thị form chỉnh sửa
  const handleShowEditForm = useCallback((server: EmailServerConfiguration) => {
    setServerToEdit(server);
    showEditForm();
  }, [showEditForm]);

  // Xử lý hiển thị form xem chi tiết
  const handleShowViewForm = useCallback((server: EmailServerConfiguration) => {
    setServerToView(server);
    showViewForm();
  }, [showViewForm]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<EmailServerConfiguration>[]>(() => {
    const allColumns: TableColumn<EmailServerConfiguration>[] = [
      {
        key: 'serverName',
        title: t('admin:integration.email.list.columns.serverName'),
        dataIndex: 'serverName',
        width: '20%',
        sortable: true,
      },
      {
        key: 'host',
        title: t('admin:integration.email.list.columns.host'),
        dataIndex: 'host',
        width: '20%',
        sortable: true,
      },
      {
        key: 'port',
        title: t('admin:integration.email.list.columns.port'),
        dataIndex: 'port',
        width: '10%',
        sortable: true,
      },
      {
        key: 'username',
        title: t('admin:integration.email.list.columns.username'),
        dataIndex: 'username',
        width: '20%',
        sortable: true,
      },
      {
        key: 'useSsl',
        title: t('admin:integration.email.list.columns.ssl'),
        dataIndex: 'useSsl',
        width: '10%',
        render: (value: unknown) => (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {value ? 'SSL' : 'No SSL'}
          </span>
        ),
      },
      {
        key: 'isActive',
        title: t('admin:integration.email.list.columns.status'),
        dataIndex: 'isActive',
        width: '10%',
        render: (value: unknown) => (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {value ? t('common:active') : t('common:inactive')}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('admin:integration.email.list.columns.actions'),
        render: (_: unknown, record: EmailServerConfiguration) => {
          const menuItems = [
            {
              label: t('admin:integration.email.actions.edit'),
              icon: 'edit',
              onClick: () => handleShowEditForm(record),
            },
            {
              label: t('common:view'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              label: t('admin:integration.email.actions.delete'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowEditForm, handleShowViewForm]);

  // Sử dụng hook useDataTable để quản lý dữ liệu bảng
  const dataTableConfig = useDataTableConfig<EmailServerConfiguration, EmailServerQueryParams>({
    columns,
    filterOptions: [],
    showDateFilter: false,
    createQueryParams: params => ({
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
    }),
  });

  const dataTable = useDataTable(dataTableConfig);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Tạo query params cho API
  const queryParams = useMemo<EmailServerQueryParams>(() => {
    const params: EmailServerQueryParams = {
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
      search: dataTable.tableData.searchTerm || undefined,
    };

    return params;
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
  ]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearSort, handleClearAll } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Hooks để gọi API
  const {
    data: emailServersData,
    isLoading: isLoadingServers,
    error: serversError,
  } = useEmailServers(queryParams);

  const createEmailServerMutation = useCreateEmailServer();
  const updateEmailServerMutation = useUpdateEmailServer();
  const deleteEmailServerMutation = useDeleteEmailServer();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (emailServersData?.result) {
      // API mới luôn trả về cấu trúc PaginatedResult
      setEmailServers(emailServersData.result.items || []);
      setTotalItems(emailServersData.result.meta?.totalItems || 0);
    }

    setIsLoading(isLoadingServers);
  }, [emailServersData, serversError, isLoadingServers]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      dataTable.tableData.handlePageChange(page, newPageSize);
    },
    [dataTable.tableData]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback(
    (term: string) => {
      dataTable.tableData.handleSearch(term);
    },
    [dataTable.tableData]
  );

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback(
    (column: string | null, order: SortOrder) => {
      dataTable.tableData.handleSortChange(column, order);
    },
    [dataTable.tableData]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setServerToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!serverToDelete) return;

    try {
      await deleteEmailServerMutation.mutateAsync(serverToDelete.id);
      setShowDeleteConfirm(false);
      setServerToDelete(null);
    } catch (error) {
      console.error('Error deleting email server:', error);
    }
  }, [serverToDelete, deleteEmailServerMutation]);

  // Xử lý submit form tạo mới
  const handleSubmitCreateServer = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsSubmitting(true);
        const createData: CreateEmailServerDto = values as unknown as CreateEmailServerDto;

        await createEmailServerMutation.mutateAsync(createData);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating email server:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createEmailServerMutation, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa
  const handleSubmitEditServer = useCallback(
    async (values: Record<string, unknown>) => {
      if (!serverToEdit) return;

      try {
        setIsSubmitting(true);
        const updateData: UpdateEmailServerDto = values as unknown as UpdateEmailServerDto;

        await updateEmailServerMutation.mutateAsync({
          id: serverToEdit.id,
          data: updateData,
        });
        hideEditForm();
        setServerToEdit(null);
      } catch (error) {
        console.error('Error updating email server:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [serverToEdit, updateEmailServerMutation, hideEditForm]
  );

  // Xử lý wizard completion
  const handleWizardComplete = useCallback(
    async (data: CreateEmailServerV2Dto) => {
      try {
        setIsSubmitting(true);

        // Convert V2 DTO to legacy DTO for backward compatibility
        const legacyData: CreateEmailServerDto = {
          serverName: data.serverName,
          host: data.host,
          port: data.port,
          username: data.username,
          password: data.password,
          useSsl: data.useSsl,
          useStartTls: data.useStartTls,
          isActive: data.isActive,
          additionalSettings: {
            ...data.additionalSettings,
            ...(data.providerId && { providerId: data.providerId }),
            ...(data.providerName && { providerName: data.providerName }),
            ...(data.authMethod && { authMethod: data.authMethod }),
            ...(data.apiKey && { apiKey: data.apiKey }),
            ...data.providerSettings,
          },
        };

        await createEmailServerMutation.mutateAsync(legacyData);
        setShowWizard(false);
      } catch (error) {
        console.error('Error creating email server via wizard:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createEmailServerMutation]
  );

  // Xử lý cancel wizard
  const handleWizardCancel = useCallback(() => {
    setShowWizard(false);
  }, []);

  // Xử lý toggle wizard mode
  const handleToggleWizardMode = useCallback(() => {
    setUseWizardMode(!useWizardMode);
  }, [useWizardMode]);

  // Xử lý show create form/wizard
  const handleShowCreate = useCallback(() => {
    if (useWizardMode) {
      setShowWizard(true);
    } else {
      showCreateForm();
    }
  }, [useWizardMode, showCreateForm]);

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setTimeout(() => {
      setServerToEdit(null);
    }, 300);
  }, [hideEditForm]);

  // Xử lý đóng form xem chi tiết
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    setTimeout(() => {
      setServerToView(null);
    }, 300);
  }, [hideViewForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo<TableColumn<EmailServerConfiguration>[]>(() => {
    if (visibleColumns.length === 0) {
      setVisibleColumns([
        { id: 'all', label: t('common:all'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* Header with mode toggle */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <Typography variant="h5">
                {t('admin:integration.email.title')}
              </Typography>
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleWizardMode}
              >
                <Icon name={useWizardMode ? 'settings' : 'magic-wand'} className="w-4 h-4 mr-2" />
                {useWizardMode ? t('integration:switchToLegacy') : t('integration:switchToWizard')}
              </Button>
            </div>
          </div>

          {/* MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={handleShowCreate}
            items={[
              {
                id: 'all',
                label: t('common:all'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          {/* ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection as SortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <EmailServerForm
            onSubmit={handleSubmitCreateServer}
            onCancel={hideCreateForm}
            isSubmitting={isSubmitting}
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {serverToEdit && (
            <EmailServerForm
              initialData={serverToEdit}
              onSubmit={handleSubmitEditServer}
              onCancel={handleCloseEditForm}
              isSubmitting={isSubmitting}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {serverToView && (
            <EmailServerForm
              initialData={serverToView}
              onSubmit={() => {}}
              onCancel={handleCloseViewForm}
              readOnly={true}
            />
          )}
        </SlideInForm>

        {/* Email Server Wizard */}
        {showWizard && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-auto">
              <EmailServerWizard
                onComplete={handleWizardComplete}
                onCancel={handleWizardCancel}
                isSubmitting={isSubmitting}
              />
            </div>
          </div>
        )}

        <Card className="overflow-hidden">
          <Table<EmailServerConfiguration>
            columns={filteredColumns}
            data={emailServers}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:integration.email.confirmations.deleteTitle')}
        message={t('admin:integration.email.confirmations.delete')}
        itemName={serverToDelete?.serverName}
      />
    </div>
  );
};

export default EmailServerManagementPage;
