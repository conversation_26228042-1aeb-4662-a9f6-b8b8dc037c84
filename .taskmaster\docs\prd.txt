# Email Server Integration Enhancement - Product Requirements Document

## 1. Tổng quan dự án

### 1.1 Mục tiêu
Nâng cấp giao diện tạo Email Server trong module /integrations/email để hỗ trợ nhiều nhà cung cấp email phổ biến thay vì chỉ cấu hình SMTP thủ công.

### 1.2 Vấn đề hiện tại
- Hiện tại chỉ hỗ trợ cấu hình SMTP thủ công với các trường: host, port, username, password
- Người dùng phải tự tìm hiểu cấu hình SMTP của từng nhà cung cấp
- Không có template sẵn cho các nhà cung cấp phổ biến
- Giao diện không thân thiện với người dùng không am hiểu kỹ thuật

### 1.3 Giải pháp đề xuất
Xây dựng giao diện wizard-style cho phép người dùng:
- Chọn nhà cung cấp email từ danh sách có sẵn
- Tự động điền cấu hình SMTP dựa trên nhà cung cấp được chọn
- Vẫn cho phép cấu hình thủ công cho các trường hợp đặc biệt
- Hướng dẫn chi tiết cho từng nhà cung cấp

## 2. Yêu cầu chức năng

### 2.1 Danh sách nhà cung cấp hỗ trợ
**Tier 1 - Ưu tiên cao:**
- Gmail/Google Workspace
- Outlook/Microsoft 365
- Yahoo Mail
- SendGrid
- Mailgun
- Amazon SES

**Tier 2 - Ưu tiên trung bình:**
- Mailchimp Transactional (Mandrill)
- Twilio SendGrid
- Postmark
- SparkPost
- Zoho Mail

**Tier 3 - Ưu tiên thấp:**
- Custom SMTP (cấu hình thủ công hiện tại)
- iCloud Mail
- ProtonMail
- FastMail

### 2.2 Luồng người dùng
1. **Bước 1: Chọn loại cấu hình**
   - Provider Templates (Recommended)
   - Custom SMTP Configuration

2. **Bước 2: Chọn nhà cung cấp** (nếu chọn Provider Templates)
   - Hiển thị grid các nhà cung cấp với logo và tên
   - Tìm kiếm nhà cung cấp
   - Phân loại theo: Email Services, Business Email, Transactional Email

3. **Bước 3: Cấu hình chi tiết**
   - Tự động điền SMTP settings dựa trên provider
   - Form fields tùy chỉnh theo từng provider
   - Hướng dẫn lấy credentials từ provider
   - Test connection trước khi lưu

4. **Bước 4: Xác nhận và lưu**
   - Review cấu hình
   - Test gửi email
   - Lưu cấu hình

### 2.3 Cấu trúc dữ liệu mới
```typescript
interface EmailProvider {
  id: string;
  name: string;
  logo: string;
  category: 'business' | 'transactional' | 'personal';
  tier: 1 | 2 | 3;
  defaultConfig: {
    host: string;
    port: number;
    useSsl: boolean;
    useStartTls: boolean;
  };
  authMethods: ('password' | 'oauth' | 'apikey')[];
  setupGuide: string;
  documentationUrl: string;
}

interface EmailServerConfigurationV2 extends EmailServerConfiguration {
  providerId?: string;
  providerName?: string;
  authMethod?: 'password' | 'oauth' | 'apikey';
  apiKey?: string;
  oauthTokens?: {
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
  };
}
```

## 3. Yêu cầu kỹ thuật

### 3.1 Frontend Components
- **ProviderSelectionStep**: Component chọn nhà cung cấp
- **ProviderConfigurationStep**: Form cấu hình theo provider
- **EmailServerWizard**: Wizard container
- **ProviderCard**: Card hiển thị thông tin provider
- **SetupGuideModal**: Modal hướng dẫn setup

### 3.2 Backend API Extensions
- GET /email-providers: Lấy danh sách providers
- POST /email-server/validate-provider: Validate cấu hình provider
- POST /email-server/oauth/authorize: OAuth flow cho providers hỗ trợ
- GET /email-server/setup-guide/{providerId}: Lấy hướng dẫn setup

### 3.3 Database Schema Updates
```sql
-- Bảng email_providers
CREATE TABLE email_providers (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  logo_url VARCHAR(255),
  category ENUM('business', 'transactional', 'personal'),
  tier TINYINT,
  default_config JSON,
  auth_methods JSON,
  setup_guide TEXT,
  documentation_url VARCHAR(255),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Cập nhật bảng email_server_configurations
ALTER TABLE email_server_configurations 
ADD COLUMN provider_id VARCHAR(50),
ADD COLUMN provider_name VARCHAR(100),
ADD COLUMN auth_method ENUM('password', 'oauth', 'apikey'),
ADD COLUMN api_key VARCHAR(255),
ADD COLUMN oauth_tokens JSON,
ADD FOREIGN KEY (provider_id) REFERENCES email_providers(id);
```

## 4. Yêu cầu UI/UX

### 4.1 Design System
- Sử dụng existing design system của RedAI
- Responsive design cho mobile và desktop
- Dark/Light theme support
- Accessibility compliance (WCAG 2.1)

### 4.2 User Experience
- Wizard-style interface với progress indicator
- Auto-save draft configurations
- Inline validation và error handling
- Contextual help và tooltips
- Quick setup cho popular providers

### 4.3 Visual Design
- Provider logos và branding colors
- Clear visual hierarchy
- Loading states và progress indicators
- Success/error states với appropriate messaging

## 5. Acceptance Criteria

### 5.1 Functional Requirements
- [ ] Người dùng có thể chọn từ danh sách 15+ email providers
- [ ] Tự động điền SMTP configuration cho mỗi provider
- [ ] Hỗ trợ multiple authentication methods
- [ ] Test connection functionality cho tất cả providers
- [ ] Backward compatibility với existing configurations
- [ ] Import/Export configuration functionality

### 5.2 Performance Requirements
- [ ] Page load time < 2 seconds
- [ ] Provider selection response time < 500ms
- [ ] Configuration validation < 1 second
- [ ] Support 100+ concurrent users

### 5.3 Security Requirements
- [ ] Encrypt stored credentials
- [ ] Secure OAuth token handling
- [ ] API key masking trong UI
- [ ] Audit logging cho configuration changes

## 6. Implementation Phases

### Phase 1: Foundation (Week 1-2)
- Database schema updates
- Provider data seeding
- Basic API endpoints
- Core frontend components

### Phase 2: Provider Integration (Week 3-4)
- Tier 1 providers implementation
- OAuth flow cho Google/Microsoft
- Setup guides và documentation
- Testing framework

### Phase 3: Enhanced UX (Week 5-6)
- Wizard interface
- Advanced validation
- Tier 2 providers
- Mobile optimization

### Phase 4: Polish & Launch (Week 7-8)
- Tier 3 providers
- Performance optimization
- Security audit
- Documentation và training

## 7. Success Metrics
- 80% reduction in support tickets related to email configuration
- 90% of users successfully configure email in < 5 minutes
- 95% test connection success rate
- User satisfaction score > 4.5/5
