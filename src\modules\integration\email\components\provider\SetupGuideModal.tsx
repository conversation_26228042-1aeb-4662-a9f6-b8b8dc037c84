import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Typography,
  Button,
  Icon,
  Card,
  Alert,
  Chip,
} from '@/shared/components/common';
import { EmailProvider } from '../../types/providers';

interface SetupGuideModalProps {
  isOpen: boolean;
  onClose: () => void;
  provider: EmailProvider;
}

/**
 * SetupGuideModal Component
 * Modal hiển thị hướng dẫn setup chi tiết cho provider
 */
const SetupGuideModal: React.FC<SetupGuideModalProps> = ({
  isOpen,
  onClose,
  provider,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const handleStepComplete = (stepIndex: number) => {
    if (!completedSteps.includes(stepIndex)) {
      setCompletedSteps([...completedSteps, stepIndex]);
    }
    
    // Auto advance to next step
    if (stepIndex < provider.setupGuide.steps.length - 1) {
      setCurrentStepIndex(stepIndex + 1);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    setCurrentStepIndex(stepIndex);
  };

  const isStepCompleted = (stepIndex: number) => {
    return completedSteps.includes(stepIndex);
  };

  const getStepStatus = (stepIndex: number) => {
    if (isStepCompleted(stepIndex)) return 'completed';
    if (stepIndex === currentStepIndex) return 'current';
    return 'pending';
  };

  const currentStep = provider.setupGuide.steps[currentStepIndex];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={provider.setupGuide.title}
      size="xl"
      className="max-h-[90vh]"
    >
      <div className="flex flex-col h-full">
        {/* Provider Header */}
        <div className="flex items-center space-x-4 mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="w-12 h-12 flex items-center justify-center bg-white rounded-lg shadow-sm">
            {provider.logoUrl ? (
              <img
                src={provider.logoUrl}
                alt={provider.displayName}
                className="w-8 h-8 object-contain"
              />
            ) : (
              <Icon name="mail" className="w-6 h-6 text-gray-500" />
            )}
          </div>
          <div className="flex-1">
            <Typography variant="h5" className="mb-1">
              {provider.displayName}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground mb-2">
              {provider.setupGuide.description}
            </Typography>
            <div className="flex items-center space-x-2">
              <Chip outlined size="sm">
                {t(`integration:provider.category.${provider.category}`)}
              </Chip>
              {provider.authMethods.map(method => (
                <Chip key={method} outlined size="sm">
                  {method === 'oauth' ? 'OAuth' : method === 'apikey' ? 'API Key' : 'Password'}
                </Chip>
              ))}
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="mb-6">
          <Typography variant="h6" className="mb-4">
            {t('integration:setupGuide.progress')}
          </Typography>
          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            {provider.setupGuide.steps.map((step, index) => {
              const isActive = index === currentStepIndex;
              const isCompleted = isStepCompleted(index);

              return (
                <div
                  key={index}
                  className="flex-shrink-0 cursor-pointer flex items-center space-x-2"
                  onClick={() => handleStepClick(index)}
                >
                  <div className={`
                    flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors
                    ${isCompleted ? 'bg-success border-success text-white' :
                      isActive ? 'bg-primary border-primary text-white' :
                      'bg-background border-border text-muted-foreground'}
                  `}>
                    {isCompleted ? (
                      <Icon name="check" className="w-4 h-4" />
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>
                  <Typography variant="caption" className={`
                    ${isActive ? 'text-primary font-medium' :
                      isCompleted ? 'text-success' : 'text-muted-foreground'}
                  `}>
                    {step.title}
                  </Typography>
                  {index < provider.setupGuide.steps.length - 1 && (
                    <div className={`w-8 h-0.5 ${isCompleted ? 'bg-success' : 'bg-border'}`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Current Step Content */}
        <div className="flex-1 overflow-y-auto">
          <Card className="mb-6">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <Typography variant="h5">
                  {t('integration:setupGuide.step')} {currentStepIndex + 1}: {currentStep.title}
                </Typography>
                {isStepCompleted(currentStepIndex) && (
                  <Chip variant="success" size="sm">
                    <Icon name="check" className="w-3 h-3 mr-1" />
                    {t('integration:setupGuide.completed')}
                  </Chip>
                )}
              </div>

              <Typography variant="body1" className="text-muted-foreground mb-4">
                {currentStep.description}
              </Typography>

              {/* Step Image */}
              {currentStep.imageUrl && (
                <div className="mb-4">
                  <img
                    src={currentStep.imageUrl}
                    alt={currentStep.title}
                    className="w-full max-w-md mx-auto rounded-lg border shadow-sm"
                  />
                </div>
              )}

              {/* Code Block */}
              {currentStep.code && (
                <div className="mb-4">
                  <Typography variant="label" className="mb-2 block">
                    {t('integration:setupGuide.code')}:
                  </Typography>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                    <pre>{currentStep.code}</pre>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => navigator.clipboard.writeText(currentStep.code || '')}
                  >
                    <Icon name="copy" className="w-3 h-3 mr-1" />
                    {t('integration:setupGuide.copyCode')}
                  </Button>
                </div>
              )}

              {/* Tips */}
              {currentStep.tips && currentStep.tips.length > 0 && (
                <Alert variant="info" className="mb-4">
                  <div>
                    <Typography variant="body2" className="font-medium mb-2">
                      <Icon name="lightbulb" className="w-4 h-4 mr-1 inline" />
                      {t('integration:setupGuide.tips')}:
                    </Typography>
                    <ul className="list-disc list-inside space-y-1">
                      {currentStep.tips.map((tip, index) => (
                        <li key={index} className="text-sm text-muted-foreground">
                          {tip}
                        </li>
                      ))}
                    </ul>
                  </div>
                </Alert>
              )}

              {/* Step Actions */}
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="flex space-x-2">
                  {currentStepIndex > 0 && (
                    <Button
                      variant="outline"
                      onClick={() => setCurrentStepIndex(currentStepIndex - 1)}
                    >
                      <Icon name="arrow-left" className="w-4 h-4 mr-2" />
                      {t('integration:setupGuide.previousStep')}
                    </Button>
                  )}
                </div>

                <div className="flex space-x-2">
                  {!isStepCompleted(currentStepIndex) && (
                    <Button
                      variant="success"
                      onClick={() => handleStepComplete(currentStepIndex)}
                    >
                      <Icon name="check" className="w-4 h-4 mr-2" />
                      {t('integration:setupGuide.markComplete')}
                    </Button>
                  )}
                  
                  {currentStepIndex < provider.setupGuide.steps.length - 1 && (
                    <Button
                      variant="primary"
                      onClick={() => setCurrentStepIndex(currentStepIndex + 1)}
                    >
                      {t('integration:setupGuide.nextStep')}
                      <Icon name="arrow-right" className="w-4 h-4 ml-2" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Card>

          {/* Additional Resources */}
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4">
                {t('integration:setupGuide.additionalResources')}
              </Typography>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  className="justify-start h-auto p-4"
                  onClick={() => window.open(provider.setupGuide.documentationUrl, '_blank')}
                >
                  <div className="text-left">
                    <div className="flex items-center mb-1">
                      <Icon name="book-open" className="w-4 h-4 mr-2" />
                      <Typography variant="body2" className="font-medium">
                        {t('integration:setupGuide.officialDocs')}
                      </Typography>
                    </div>
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('integration:setupGuide.officialDocsDesc')}
                    </Typography>
                  </div>
                  <Icon name="external-link" className="w-4 h-4 ml-auto" />
                </Button>

                {provider.setupGuide.troubleshootingUrl && (
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                    onClick={() => window.open(provider.setupGuide.troubleshootingUrl, '_blank')}
                  >
                    <div className="text-left">
                      <div className="flex items-center mb-1">
                        <Icon name="help-circle" className="w-4 h-4 mr-2" />
                        <Typography variant="body2" className="font-medium">
                          {t('integration:setupGuide.troubleshooting')}
                        </Typography>
                      </div>
                      <Typography variant="caption" className="text-muted-foreground">
                        {t('integration:setupGuide.troubleshootingDesc')}
                      </Typography>
                    </div>
                    <Icon name="external-link" className="w-4 h-4 ml-auto" />
                  </Button>
                )}

                {provider.pricing?.url && (
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                    onClick={() => window.open(provider.pricing.url, '_blank')}
                  >
                    <div className="text-left">
                      <div className="flex items-center mb-1">
                        <Icon name="credit-card" className="w-4 h-4 mr-2" />
                        <Typography variant="body2" className="font-medium">
                          {t('integration:setupGuide.pricing')}
                        </Typography>
                      </div>
                      <Typography variant="caption" className="text-muted-foreground">
                        {provider.pricing.free || provider.pricing.paid}
                      </Typography>
                    </div>
                    <Icon name="external-link" className="w-4 h-4 ml-auto" />
                  </Button>
                )}

                <Button
                  variant="outline"
                  className="justify-start h-auto p-4"
                  onClick={() => {
                    // Reset progress and start over
                    setCurrentStepIndex(0);
                    setCompletedSteps([]);
                  }}
                >
                  <div className="text-left">
                    <div className="flex items-center mb-1">
                      <Icon name="refresh-cw" className="w-4 h-4 mr-2" />
                      <Typography variant="body2" className="font-medium">
                        {t('integration:setupGuide.startOver')}
                      </Typography>
                    </div>
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('integration:setupGuide.startOverDesc')}
                    </Typography>
                  </div>
                </Button>
              </div>
            </div>
          </Card>
        </div>

        {/* Modal Footer */}
        <div className="flex items-center justify-between pt-6 border-t mt-6">
          <div className="flex items-center space-x-2">
            <Typography variant="caption" className="text-muted-foreground">
              {t('integration:setupGuide.progress')}: {completedSteps.length}/{provider.setupGuide.steps.length}
            </Typography>
            <div className="w-32 bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${(completedSteps.length / provider.setupGuide.steps.length) * 100}%`,
                }}
              />
            </div>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              {t('common:close')}
            </Button>
            {completedSteps.length === provider.setupGuide.steps.length && (
              <Button variant="success" onClick={onClose}>
                <Icon name="check" className="w-4 h-4 mr-2" />
                {t('integration:setupGuide.allDone')}
              </Button>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default SetupGuideModal;
