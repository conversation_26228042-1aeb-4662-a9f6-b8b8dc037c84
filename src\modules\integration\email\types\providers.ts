/**
 * Email Provider Types
 */

/**
 * Email Provider Categories
 */
export type EmailProviderCategory = 'personal' | 'business' | 'transactional';

/**
 * Email Provider Tiers
 */
export type EmailProviderTier = 1 | 2 | 3;

/**
 * Authentication Methods supported by providers
 */
export type ProviderAuthMethod = 'password' | 'oauth' | 'apikey';

/**
 * OAuth Token Structure
 */
export interface OAuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
  tokenType?: string;
  scope?: string;
}

/**
 * Default SMTP Configuration for a provider
 */
export interface ProviderDefaultConfig {
  host: string;
  port: number;
  useSsl: boolean;
  useStartTls: boolean;
  requiresAuth: boolean;
}

/**
 * Provider-specific field configuration
 */
export interface ProviderField {
  name: string;
  label: string;
  type: 'text' | 'password' | 'email' | 'number' | 'select' | 'textarea';
  required: boolean;
  placeholder?: string;
  description?: string;
  options?: { label: string; value: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

/**
 * Setup Guide Step
 */
export interface SetupGuideStep {
  title: string;
  description: string;
  imageUrl?: string;
  code?: string;
  tips?: string[];
}

/**
 * Email Provider Definition
 */
export interface EmailProvider {
  id: string;
  name: string;
  displayName: string;
  description: string;
  logo: string;
  logoUrl?: string;
  category: EmailProviderCategory;
  tier: EmailProviderTier;
  isPopular: boolean;
  defaultConfig: ProviderDefaultConfig;
  authMethods: ProviderAuthMethod[];
  customFields?: ProviderField[];
  setupGuide: {
    title: string;
    description: string;
    steps: SetupGuideStep[];
    documentationUrl: string;
    troubleshootingUrl?: string;
  };
  features: string[];
  limitations?: string[];
  pricing?: {
    free?: string;
    paid?: string;
    url?: string;
  };
  regions?: string[];
  isActive: boolean;
}

/**
 * Provider Selection Filter
 */
export interface ProviderFilter {
  category?: EmailProviderCategory;
  tier?: EmailProviderTier;
  authMethod?: ProviderAuthMethod;
  search?: string;
  isPopular?: boolean;
}

/**
 * Provider Configuration Data
 */
export interface ProviderConfigurationData {
  providerId: string;
  providerName: string;
  authMethod: ProviderAuthMethod;
  credentials: {
    username?: string;
    password?: string;
    apiKey?: string;
    clientId?: string;
    clientSecret?: string;
  };
  oauthTokens?: OAuthTokens;
  customSettings?: Record<string, string | number | boolean>;
}

/**
 * Extended Email Server Configuration with Provider Support
 */
export interface EmailServerConfigurationV2 {
  id: number;
  userId: number;
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls: boolean;
  isActive: boolean;
  additionalSettings?: Record<string, string | number | boolean>;
  
  // Provider-specific fields
  providerId?: string;
  providerName?: string;
  authMethod?: ProviderAuthMethod;
  apiKey?: string;
  oauthTokens?: OAuthTokens;
  providerSettings?: Record<string, string | number | boolean>;
  
  createdAt: string;
  updatedAt: string;
}

/**
 * Create Email Server DTO with Provider Support
 */
export interface CreateEmailServerV2Dto {
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls: boolean;
  isActive: boolean;
  additionalSettings?: Record<string, string | number | boolean>;
  
  // Provider-specific fields
  providerId?: string;
  providerName?: string;
  authMethod?: ProviderAuthMethod;
  apiKey?: string;
  oauthTokens?: OAuthTokens;
  providerSettings?: Record<string, string | number | boolean>;
}

/**
 * Update Email Server DTO with Provider Support
 */
export interface UpdateEmailServerV2Dto {
  serverName?: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  useSsl?: boolean;
  useStartTls?: boolean;
  isActive?: boolean;
  additionalSettings?: Record<string, string | number | boolean>;
  
  // Provider-specific fields
  providerId?: string;
  providerName?: string;
  authMethod?: ProviderAuthMethod;
  apiKey?: string;
  oauthTokens?: OAuthTokens;
  providerSettings?: Record<string, string | number | boolean>;
}

/**
 * Provider Validation Result
 */
export interface ProviderValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
  suggestions?: string[];
}

/**
 * Provider Test Connection Data
 */
export interface ProviderTestConnectionData {
  providerId: string;
  configuration: ProviderConfigurationData;
  testEmail: {
    recipientEmail: string;
    subject?: string;
    body?: string;
  };
}
